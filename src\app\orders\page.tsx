'use client';

import React, { useState, useMemo } from 'react';
import {
  PlusIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { useOrders, useUpdateOrderStatus, useAssignProvider, useCancelOrder } from '@/hooks/useOrders';
import { useProviders } from '@/hooks/useProviders';
import { formatCurrency, formatDateTime, formatRelativeTime } from '@/lib/utils';
import { Order, PaginatedResponse } from '@/types/api';
import ApiDebugger from '@/components/debug/ApiDebugger';

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('');
  const [dateRangeFilter, setDateRangeFilter] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showAssignProvider, setShowAssignProvider] = useState(false);

  // Prepare query parameters
  const queryParams = useMemo(() => {
    const params: Record<string, any> = {
      ordering: '-created_at',
      limit: 20,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (statusFilter) {
      params.status = statusFilter;
    }

    if (paymentStatusFilter) {
      params.payment_status = paymentStatusFilter;
    }

    if (dateRangeFilter) {
      const today = new Date();
      switch (dateRangeFilter) {
        case 'today':
          params.created_at__date = today.toISOString().split('T')[0];
          break;
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          params.created_at__gte = weekAgo.toISOString().split('T')[0];
          break;
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
          params.created_at__gte = monthAgo.toISOString().split('T')[0];
          break;
      }
    }

    return params;
  }, [searchTerm, statusFilter, paymentStatusFilter, dateRangeFilter, page]);

  // Use React Query hooks
  const { data: ordersResponse, isLoading, error } = useOrders(queryParams);
  const { data: providersResponse } = useProviders({ limit: 100 });
  const updateOrderStatusMutation = useUpdateOrderStatus();
  const assignProviderMutation = useAssignProvider();
  const cancelOrderMutation = useCancelOrder();

  const orders = ordersResponse?.results || [];
  const providers = providersResponse?.results || [];
  const totalItems = ordersResponse?.count || 0;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  const handleStatusChange = (orderNumber: string, newStatus: string) => {
    updateOrderStatusMutation.mutate({
      orderNumber,
      data: { status: newStatus }
    });
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const handleAssignProvider = (order: Order) => {
    setSelectedOrder(order);
    setShowAssignProvider(true);
  };

  // Define table columns
  const columns: Column<Order>[] = [
    {
      key: 'order_number',
      label: 'Order',
      sortable: true,
      render: (_, order) => (
        <div>
          <div className="font-medium text-gray-900">#{order.order_number}</div>
          <div className="text-sm text-gray-500">{order.items_count} items</div>
        </div>
      ),
    },
    {
      key: 'customer_name',
      label: 'Customer',
      render: (_, order) => (
        <div>
          <div className="font-medium text-gray-900">{order.customer_name}</div>
          <div className="text-sm text-gray-500">{order.customer_mobile}</div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <Badge
          variant={
            value === 'completed' ? 'success' :
            value === 'cancelled' ? 'danger' :
            value === 'in_progress' ? 'info' :
            'warning'
          }
        >
          {value}
        </Badge>
      ),
    },
    {
      key: 'total_amount',
      label: 'Amount',
      sortable: true,
      render: (_, order) => (
        <div>
          <div className="font-medium text-gray-900">
            {formatCurrency(order.total_amount)}
          </div>
          <div className="text-sm text-gray-500">
            {order.payment_method ? order.payment_method.toUpperCase() : 'N/A'}
          </div>
        </div>
      ),
    },
    {
      key: 'provider_name',
      label: 'Provider',
      render: (value) => (
        <div className="text-sm text-gray-900">
          {value || 'Not assigned'}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Date',
      sortable: true,
      render: (value) => (
        <div>
          <div className="text-sm text-gray-900">{formatDateTime(value)}</div>
          <div className="text-xs text-gray-500">{formatRelativeTime(value)}</div>
        </div>
      ),
    },
  ];

  // Define row actions
  const rowActions = (order: Order) => (
    <div className="flex items-center space-x-2">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleViewOrder(order)}
        title="View Details"
      >
        <EyeIcon className="w-4 h-4" />
      </Button>

      {order.status === 'pending' && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleStatusChange(order.order_number, 'confirmed')}
          title="Confirm Order"
        >
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        </Button>
      )}

      {(order.status === 'confirmed' || order.status === 'assigned') && !order.provider_name && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleAssignProvider(order)}
          title="Assign Provider"
        >
          <UserIcon className="w-4 h-4 text-blue-500" />
        </Button>
      )}

      {order.status !== 'completed' && order.status !== 'cancelled' && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            if (confirm('Are you sure you want to cancel this order?')) {
              cancelOrderMutation.mutate({
                orderNumber: order.order_number,
                data: { reason: 'Cancelled by admin' }
              });
            }
          }}
          title="Cancel Order"
        >
          <XCircleIcon className="w-4 h-4 text-red-500" />
        </Button>
      )}
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Order Management</h1>
          <Button>
            <PlusIcon className="w-4 h-4 mr-2" />
            Create Order
          </Button>
        </div>



        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Order Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="assigned">Assigned</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Status
              </label>
              <select
                value={paymentStatusFilter}
                onChange={(e) => {
                  setPaymentStatusFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Payments</option>
                <option value="pending">Pending</option>
                <option value="paid">Paid</option>
                <option value="failed">Failed</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date Range
              </label>
              <select
                value={dateRangeFilter}
                onChange={(e) => {
                  setDateRangeFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Time</option>
                <option value="today">Today</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
              </select>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <DataTable
          data={orders}
          columns={columns}
          loading={isLoading}
          error={error ? (error as any).message : undefined}
          searchable
          searchPlaceholder="Search orders by number, customer name, or phone..."
          onSearch={handleSearch}
          sortable
          pagination={{
            page,
            totalPages,
            totalItems,
            itemsPerPage: 20,
            onPageChange: setPage,
          }}
          actions={rowActions}
          emptyState={{
            title: 'No orders found',
            description: 'No orders match your current filters.',
            action: (
              <div className="space-y-3">
                <Button onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setPaymentStatusFilter('');
                  setDateRangeFilter('');
                  setPage(1);
                }}>
                  Clear Filters
                </Button>
                <div className="text-sm text-gray-500">or</div>
                <Button>
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Create First Order
                </Button>
              </div>
            ),
          }}
        />
      </div>
    </DashboardLayout>
  );
}
