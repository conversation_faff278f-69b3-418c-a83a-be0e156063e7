'use client';

import React, { useState, useMemo } from 'react';
import { 
  PlusIcon, 
  EyeIcon, 
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  TicketIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { formatCurrency, formatDateTime, formatRelativeTime } from '@/lib/utils';
import ApiDebugger from '@/components/debug/ApiDebugger';

// Mock Coupon type for now
interface Coupon {
  id: number;
  code: string;
  name: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  minimum_order_amount?: number;
  maximum_discount_amount?: number;
  usage_limit?: number;
  usage_count: number;
  usage_limit_per_user?: number;
  valid_from: string;
  valid_until: string;
  is_active: boolean;
  created_at: string;
}

// Mock data for now
const mockCoupons: Coupon[] = [
  {
    id: 1,
    code: 'WELCOME10',
    name: 'Welcome Discount',
    discount_type: 'percentage',
    discount_value: 10,
    minimum_order_amount: 500,
    maximum_discount_amount: 100,
    usage_limit: 1000,
    usage_count: 245,
    usage_limit_per_user: 1,
    valid_from: new Date().toISOString(),
    valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    is_active: true,
    created_at: new Date().toISOString(),
  },
  {
    id: 2,
    code: 'SAVE50',
    name: 'Flat 50 Off',
    discount_type: 'fixed',
    discount_value: 50,
    minimum_order_amount: 200,
    usage_limit: 500,
    usage_count: 123,
    valid_from: new Date().toISOString(),
    valid_until: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
    is_active: true,
    created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
  },
];

export default function CouponsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [page, setPage] = useState(1);

  // Mock data for now
  const coupons = mockCoupons;
  const totalItems = coupons.length;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  // Handle coupon actions
  const handleDeleteCoupon = (couponId: number) => {
    if (confirm('Are you sure you want to delete this coupon?')) {
      console.log('Deleting coupon:', couponId);
    }
  };

  const handleToggleStatus = (coupon: Coupon) => {
    console.log('Toggling coupon status:', coupon.id);
  };

  // Define table columns
  const columns: Column<Coupon>[] = [
    {
      key: 'code',
      label: 'Coupon',
      sortable: true,
      render: (_, coupon) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
              <TicketIcon className="w-4 h-4 text-blue-600" />
            </div>
          </div>
          <div>
            <div className="font-medium text-gray-900">{coupon.code}</div>
            <div className="text-sm text-gray-500">{coupon.name}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'discount_value',
      label: 'Discount',
      render: (_, coupon) => (
        <div>
          <div className="font-medium text-gray-900">
            {coupon.discount_type === 'percentage' 
              ? `${coupon.discount_value}%` 
              : formatCurrency(coupon.discount_value)
            }
          </div>
          <div className="text-sm text-gray-500">
            {coupon.discount_type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
          </div>
        </div>
      ),
    },
    {
      key: 'usage_count',
      label: 'Usage',
      render: (_, coupon) => (
        <div>
          <div className="font-medium text-gray-900">
            {coupon.usage_count} / {coupon.usage_limit || '∞'}
          </div>
          <div className="text-sm text-gray-500">
            {coupon.usage_limit 
              ? `${Math.round((coupon.usage_count / coupon.usage_limit) * 100)}% used`
              : 'Unlimited'
            }
          </div>
        </div>
      ),
    },
    {
      key: 'minimum_order_amount',
      label: 'Min Order',
      render: (value) => (
        <div className="text-sm text-gray-900">
          {value ? formatCurrency(value) : 'No minimum'}
        </div>
      ),
    },
    {
      key: 'valid_until',
      label: 'Validity',
      render: (value, coupon) => {
        const isExpired = new Date(value) < new Date();
        return (
          <div>
            <div className={`text-sm ${isExpired ? 'text-red-600' : 'text-gray-900'}`}>
              {formatDateTime(value)}
            </div>
            <div className="text-xs text-gray-500">
              {isExpired ? 'Expired' : formatRelativeTime(value)}
            </div>
          </div>
        );
      },
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value, coupon) => {
        const isExpired = new Date(coupon.valid_until) < new Date();
        return (
          <Badge 
            variant={
              !value ? 'secondary' :
              isExpired ? 'danger' : 
              'success'
            }
          >
            {!value ? 'Inactive' : isExpired ? 'Expired' : 'Active'}
          </Badge>
        );
      },
    },
  ];

  // Define row actions
  const rowActions = (coupon: Coupon) => (
    <div className="flex items-center space-x-2">
      <Button variant="ghost" size="sm" title="View Details">
        <EyeIcon className="w-4 h-4" />
      </Button>
      <Button variant="ghost" size="sm" title="Edit Coupon">
        <PencilIcon className="w-4 h-4" />
      </Button>
      <Button variant="ghost" size="sm" title="View Analytics">
        <ChartBarIcon className="w-4 h-4 text-blue-500" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => handleToggleStatus(coupon)}
        title={coupon.is_active ? 'Deactivate' : 'Activate'}
      >
        {coupon.is_active ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => handleDeleteCoupon(coupon.id)}
        title="Delete Coupon"
      >
        <TrashIcon className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Coupon Management</h1>
          <Button>
            <PlusIcon className="w-4 h-4 mr-2" />
            Create Coupon
          </Button>
        </div>



        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="expired">Expired</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Discount Type
              </label>
              <select
                value={typeFilter}
                onChange={(e) => {
                  setTypeFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Types</option>
                <option value="percentage">Percentage</option>
                <option value="fixed">Fixed Amount</option>
              </select>
            </div>
          </div>
        </div>

        {/* Coupons Table */}
        <DataTable
          data={coupons}
          columns={columns}
          loading={false}
          searchable
          searchPlaceholder="Search coupons by code or name..."
          onSearch={handleSearch}
          sortable
          pagination={{
            page,
            totalPages,
            totalItems,
            itemsPerPage: 20,
            onPageChange: setPage,
          }}
          actions={rowActions}
          emptyState={{
            title: 'No coupons found',
            description: 'No coupons match your current filters.',
            action: (
              <div className="space-y-3">
                <Button onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setTypeFilter('');
                  setPage(1);
                }}>
                  Clear Filters
                </Button>
                <div className="text-sm text-gray-500">or</div>
                <Button>
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Create First Coupon
                </Button>
              </div>
            ),
          }}
        />
      </div>
    </DashboardLayout>
  );
}
